import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/transaction.dart' as model;
import '../models/category.dart' as model_category;
import '../models/account.dart';
import '../models/budget.dart';
import '../models/saving_goal.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'vitien.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create accounts table
    await db.execute('''
      CREATE TABLE accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        initial_balance REAL NOT NULL DEFAULT 0,
        icon TEXT NOT NULL,
        color TEXT NOT NULL,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create categories table
    await db.execute('''
      CREATE TABLE categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        icon TEXT NOT NULL,
        color TEXT NOT NULL,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        amount REAL NOT NULL,
        type TEXT NOT NULL,
        category_id INTEGER NOT NULL,
        account_id INTEGER NOT NULL,
        date INTEGER NOT NULL,
        note TEXT,
        image_path TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (category_id) REFERENCES categories (id),
        FOREIGN KEY (account_id) REFERENCES accounts (id)
      )
    ''');

    // Create budgets table
    await db.execute('''
      CREATE TABLE budgets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        category_id INTEGER,
        amount REAL NOT NULL,
        period TEXT NOT NULL,
        start_date INTEGER NOT NULL,
        end_date INTEGER NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    // Create saving_goals table
    await db.execute('''
      CREATE TABLE saving_goals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        target_amount REAL NOT NULL,
        current_amount REAL NOT NULL DEFAULT 0,
        target_date INTEGER NOT NULL,
        priority TEXT NOT NULL DEFAULT 'medium',
        is_completed INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Insert default data
    await _insertDefaultData(db);
  }

  Future<void> _insertDefaultData(Database db) async {
    final now = DateTime.now().millisecondsSinceEpoch;

    // Default accounts
    await db.insert('accounts', {
      'name': 'Tiền mặt',
      'type': 'cash',
      'initial_balance': 0,
      'icon': 'wallet',
      'color': '#4CAF50',
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('accounts', {
      'name': 'Ngân hàng',
      'type': 'bank',
      'initial_balance': 0,
      'icon': 'account_balance',
      'color': '#2196F3',
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('accounts', {
      'name': 'Ví điện tử',
      'type': 'e_wallet',
      'initial_balance': 0,
      'icon': 'phone_android',
      'color': '#FF9800',
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    // Default income categories
    final incomeCategories = [
      {'name': 'Lương', 'icon': 'work', 'color': '#4CAF50'},
      {'name': 'Thưởng', 'icon': 'card_giftcard', 'color': '#8BC34A'},
      {'name': 'Kinh doanh', 'icon': 'business', 'color': '#CDDC39'},
      {'name': 'Đầu tư', 'icon': 'trending_up', 'color': '#FFC107'},
      {'name': 'Khác', 'icon': 'more_horiz', 'color': '#FF9800'},
    ];

    for (var category in incomeCategories) {
      await db.insert('categories', {
        'name': category['name'],
        'type': 'income',
        'icon': category['icon'],
        'color': category['color'],
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      });
    }

    // Default expense categories
    final expenseCategories = [
      {'name': 'Ăn uống', 'icon': 'restaurant', 'color': '#F44336'},
      {'name': 'Đi lại', 'icon': 'directions_car', 'color': '#E91E63'},
      {'name': 'Nhà ở', 'icon': 'home', 'color': '#9C27B0'},
      {'name': 'Hóa đơn', 'icon': 'receipt', 'color': '#673AB7'},
      {'name': 'Giải trí', 'icon': 'movie', 'color': '#3F51B5'},
      {'name': 'Học tập', 'icon': 'school', 'color': '#2196F3'},
      {'name': 'Sức khỏe', 'icon': 'local_hospital', 'color': '#03DAC6'},
      {'name': 'Mua sắm', 'icon': 'shopping_cart', 'color': '#FF5722'},
      {'name': 'Tiết kiệm', 'icon': 'savings', 'color': '#4CAF50'},
      {'name': 'Khác', 'icon': 'more_horiz', 'color': '#795548'},
    ];

    for (var category in expenseCategories) {
      await db.insert('categories', {
        'name': category['name'],
        'type': 'expense',
        'icon': category['icon'],
        'color': category['color'],
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      });
    }
  }

  // Transaction CRUD operations
  Future<int> insertTransaction(model.Transaction transaction) async {
    final db = await database;
    return await db.insert('transactions', transaction.toMap());
  }

  Future<List<model.Transaction>> getTransactions({
    int? limit,
    int? offset,
    String? type,
    int? categoryId,
    int? accountId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await database;
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (type != null) {
      whereClause += 'type = ?';
      whereArgs.add(type);
    }

    if (categoryId != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'category_id = ?';
      whereArgs.add(categoryId);
    }

    if (accountId != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'account_id = ?';
      whereArgs.add(accountId);
    }

    if (startDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'date >= ?';
      whereArgs.add(startDate.millisecondsSinceEpoch);
    }

    if (endDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'date <= ?';
      whereArgs.add(endDate.millisecondsSinceEpoch);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: whereClause.isEmpty ? null : whereClause,
      whereArgs: whereArgs.isEmpty ? null : whereArgs,
      orderBy: 'date DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) => model.Transaction.fromMap(maps[i]));
  }

  Future<int> updateTransaction(model.Transaction transaction) async {
    final db = await database;
    return await db.update(
      'transactions',
      transaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  Future<int> deleteTransaction(int id) async {
    final db = await database;
    return await db.delete(
      'transactions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Category CRUD operations
  Future<int> insertCategory(model_category.Category category) async {
    final db = await database;
    return await db.insert('categories', category.toMap());
  }

  Future<List<model_category.Category>> getCategories({String? type}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: type != null ? 'type = ?' : null,
      whereArgs: type != null ? [type] : null,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => model_category.Category.fromMap(maps[i]));
  }

  Future<int> updateCategory(model_category.Category category) async {
    final db = await database;
    return await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<int> deleteCategory(int id) async {
    final db = await database;
    return await db.delete(
      'categories',
      where: 'id = ? AND is_default = 0',
      whereArgs: [id],
    );
  }

  Future<model_category.Category?> getSavingsCategory() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: 'name = ? AND type = ?',
      whereArgs: ['Tiết kiệm', 'expense'],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return model_category.Category.fromMap(maps.first);
    }

    // If savings category doesn't exist, create it
    final now = DateTime.now();
    final savingsCategory = model_category.Category(
      name: 'Tiết kiệm',
      type: 'expense',
      icon: 'savings',
      color: '#4CAF50',
      isDefault: true,
      createdAt: now,
      updatedAt: now,
    );

    final id = await insertCategory(savingsCategory);
    if (id > 0) {
      return savingsCategory.copyWith(id: id);
    }

    return null;
  }

  // Account CRUD operations
  Future<int> insertAccount(Account account) async {
    final db = await database;
    return await db.insert('accounts', account.toMap());
  }

  Future<List<Account>> getAccounts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<int> updateAccount(Account account) async {
    final db = await database;
    return await db.update(
      'accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  Future<int> deleteAccount(int id) async {
    final db = await database;
    return await db.delete(
      'accounts',
      where: 'id = ? AND is_default = 0',
      whereArgs: [id],
    );
  }

  // Budget CRUD operations
  Future<int> insertBudget(Budget budget) async {
    final db = await database;
    return await db.insert('budgets', budget.toMap());
  }

  Future<List<Budget>> getBudgets({bool? isActive}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'budgets',
      where: isActive != null ? 'is_active = ?' : null,
      whereArgs: isActive != null ? [isActive ? 1 : 0] : null,
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) => Budget.fromMap(maps[i]));
  }

  Future<int> updateBudget(Budget budget) async {
    final db = await database;
    return await db.update(
      'budgets',
      budget.toMap(),
      where: 'id = ?',
      whereArgs: [budget.id],
    );
  }

  Future<int> deleteBudget(int id) async {
    final db = await database;
    return await db.delete(
      'budgets',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Saving Goal CRUD operations
  Future<int> insertSavingGoal(SavingGoal goal) async {
    final db = await database;
    return await db.insert('saving_goals', goal.toMap());
  }

  Future<List<SavingGoal>> getSavingGoals({bool? isCompleted}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'saving_goals',
      where: isCompleted != null ? 'is_completed = ?' : null,
      whereArgs: isCompleted != null ? [isCompleted ? 1 : 0] : null,
      orderBy: 'target_date ASC',
    );

    return List.generate(maps.length, (i) => SavingGoal.fromMap(maps[i]));
  }

  Future<int> updateSavingGoal(SavingGoal goal) async {
    final db = await database;
    return await db.update(
      'saving_goals',
      goal.toMap(),
      where: 'id = ?',
      whereArgs: [goal.id],
    );
  }

  Future<int> deleteSavingGoal(int id) async {
    final db = await database;
    return await db.delete(
      'saving_goals',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Analytics methods
  Future<double> getTotalBalance() async {
    final db = await database;
    final accounts = await getAccounts();
    double totalBalance = 0;

    for (var account in accounts) {
      totalBalance += account.initialBalance;
      
      // Add income transactions
      final incomeResult = await db.rawQuery(
        'SELECT SUM(amount) as total FROM transactions WHERE account_id = ? AND type = ?',
        [account.id, 'income']
      );
      final income = incomeResult.first['total'] as double? ?? 0;
      
      // Subtract expense transactions
      final expenseResult = await db.rawQuery(
        'SELECT SUM(amount) as total FROM transactions WHERE account_id = ? AND type = ?',
        [account.id, 'expense']
      );
      final expense = expenseResult.first['total'] as double? ?? 0;
      
      totalBalance += income - expense;
    }

    return totalBalance;
  }

  Future<Map<String, double>> getMonthlyTotals(DateTime month) async {
    final db = await database;
    final startOfMonth = DateTime(month.year, month.month, 1);
    final endOfMonth = DateTime(month.year, month.month + 1, 0, 23, 59, 59);

    final incomeResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE type = ? AND date >= ? AND date <= ?',
      ['income', startOfMonth.millisecondsSinceEpoch, endOfMonth.millisecondsSinceEpoch]
    );

    final expenseResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE type = ? AND date >= ? AND date <= ?',
      ['expense', startOfMonth.millisecondsSinceEpoch, endOfMonth.millisecondsSinceEpoch]
    );

    return {
      'income': incomeResult.first['total'] as double? ?? 0,
      'expense': expenseResult.first['total'] as double? ?? 0,
    };
  }

  Future<List<Map<String, dynamic>>> getCategoryExpenses(DateTime startDate, DateTime endDate) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT c.name, c.color, SUM(t.amount) as total
      FROM transactions t
      JOIN categories c ON t.category_id = c.id
      WHERE t.type = 'expense' AND t.date >= ? AND t.date <= ?
      GROUP BY c.id, c.name, c.color
      ORDER BY total DESC
    ''', [startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch]);

    return result;
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
