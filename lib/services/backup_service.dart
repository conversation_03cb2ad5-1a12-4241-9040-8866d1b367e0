import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../models/transaction.dart' as model;
import '../models/category.dart' as model_category;
import '../models/account.dart';
import '../models/budget.dart';
import '../models/saving_goal.dart';

class BackupService {
  static final BackupService _instance = BackupService._internal();
  factory BackupService() => _instance;
  BackupService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  Future<String> _getBackupDirectory() async {
    final directory = await getExternalStorageDirectory();
    final backupDir = Directory('${directory!.path}/Vitien_Backups');
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }
    return backupDir.path;
  }

  Future<Map<String, dynamic>> _getAllData() async {
    final transactions = await _databaseHelper.getTransactions();
    final categories = await _databaseHelper.getCategories();
    final accounts = await _databaseHelper.getAccounts();
    final budgets = await _databaseHelper.getBudgets();
    final savingGoals = await _databaseHelper.getSavingGoals();

    return {
      'version': '1.0',
      'exportDate': DateTime.now().toIso8601String(),
      'transactions': transactions.map((t) => t.toMap()).toList(),
      'categories': categories.map((c) => c.toMap()).toList(),
      'accounts': accounts.map((a) => a.toMap()).toList(),
      'budgets': budgets.map((b) => b.toMap()).toList(),
      'savingGoals': savingGoals.map((s) => s.toMap()).toList(),
    };
  }

  Future<String?> exportToJson() async {
    try {
      if (!await requestStoragePermission()) {
        throw Exception('Storage permission denied');
      }

      final data = await _getAllData();
      final jsonString = const JsonEncoder.withIndent('  ').convert(data);
      
      final backupDir = await _getBackupDirectory();
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final fileName = 'vitien_backup_$timestamp.json';
      final file = File('$backupDir/$fileName');
      
      await file.writeAsString(jsonString);
      return file.path;
    } catch (e) {
      debugPrint('Export error: $e');
      return null;
    }
  }

  Future<String?> exportToCsv() async {
    try {
      if (!await requestStoragePermission()) {
        throw Exception('Storage permission denied');
      }

      final transactions = await _databaseHelper.getTransactions();
      final categories = await _databaseHelper.getCategories();
      final accounts = await _databaseHelper.getAccounts();
      
      // Create CSV content
      final csvLines = <String>[];
      csvLines.add('Date,Type,Amount,Category,Account,Note');
      
      for (final transaction in transactions) {
        final category = categories.firstWhere(
          (c) => c.id == transaction.categoryId,
          orElse: () => model_category.Category(
            id: 0,
            name: 'Unknown',
            type: 'expense',
            icon: 'help',
            color: '#9E9E9E',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        final account = accounts.firstWhere(
          (a) => a.id == transaction.accountId,
          orElse: () => Account(
            id: 0,
            name: 'Unknown',
            type: 'cash',
            initialBalance: 0,
            icon: 'account_balance_wallet',
            color: '#9E9E9E',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        
        final csvLine = [
          transaction.date.toIso8601String().split('T')[0],
          transaction.type,
          transaction.amount.toString(),
          category.name,
          account.name,
          '"${transaction.note ?? ''}"',
        ].join(',');
        
        csvLines.add(csvLine);
      }
      
      final backupDir = await _getBackupDirectory();
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final fileName = 'vitien_transactions_$timestamp.csv';
      final file = File('$backupDir/$fileName');
      
      await file.writeAsString(csvLines.join('\n'));
      return file.path;
    } catch (e) {
      debugPrint('CSV export error: $e');
      return null;
    }
  }

  Future<bool> importFromJson(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File not found');
      }

      final jsonString = await file.readAsString();
      final data = json.decode(jsonString) as Map<String, dynamic>;
      
      // Validate backup format
      if (data['version'] == null || data['transactions'] == null) {
        throw Exception('Invalid backup format');
      }

      // Clear existing data (optional - could be made configurable)
      // await _clearAllData();

      // Import categories first
      if (data['categories'] != null) {
        for (final categoryData in data['categories']) {
          final category = model_category.Category.fromMap(categoryData);
          await _databaseHelper.insertCategory(category);
        }
      }

      // Import accounts
      if (data['accounts'] != null) {
        for (final accountData in data['accounts']) {
          final account = Account.fromMap(accountData);
          await _databaseHelper.insertAccount(account);
        }
      }

      // Import budgets
      if (data['budgets'] != null) {
        for (final budgetData in data['budgets']) {
          final budget = Budget.fromMap(budgetData);
          await _databaseHelper.insertBudget(budget);
        }
      }

      // Import saving goals
      if (data['savingGoals'] != null) {
        for (final goalData in data['savingGoals']) {
          final goal = SavingGoal.fromMap(goalData);
          await _databaseHelper.insertSavingGoal(goal);
        }
      }

      // Import transactions last
      if (data['transactions'] != null) {
        for (final transactionData in data['transactions']) {
          final transaction = model.Transaction.fromMap(transactionData);
          await _databaseHelper.insertTransaction(transaction);
        }
      }

      return true;
    } catch (e) {
      debugPrint('Import error: $e');
      return false;
    }
  }

  Future<void> _clearAllData() async {
    // Clear all tables - use with caution
    final db = await _databaseHelper.database;
    await db.delete('transactions');
    await db.delete('budgets');
    await db.delete('saving_goals');
    // Don't clear categories and accounts as they might be defaults
  }

  Future<List<String>> getAvailableBackups() async {
    try {
      final backupDir = await _getBackupDirectory();
      final directory = Directory(backupDir);
      
      if (!await directory.exists()) {
        return [];
      }

      final files = await directory.list().toList();
      final backupFiles = files
          .where((file) => file is File && file.path.endsWith('.json'))
          .map((file) => file.path)
          .toList();
      
      backupFiles.sort((a, b) => b.compareTo(a)); // Sort by newest first
      return backupFiles;
    } catch (e) {
      debugPrint('Error getting backups: $e');
      return [];
    }
  }

  String getBackupFileName(String filePath) {
    return filePath.split('/').last;
  }

  Future<Map<String, dynamic>?> getBackupInfo(String filePath) async {
    try {
      final file = File(filePath);
      final jsonString = await file.readAsString();
      final data = json.decode(jsonString) as Map<String, dynamic>;
      
      return {
        'version': data['version'],
        'exportDate': data['exportDate'],
        'transactionCount': (data['transactions'] as List?)?.length ?? 0,
        'categoryCount': (data['categories'] as List?)?.length ?? 0,
        'accountCount': (data['accounts'] as List?)?.length ?? 0,
        'budgetCount': (data['budgets'] as List?)?.length ?? 0,
        'savingGoalCount': (data['savingGoals'] as List?)?.length ?? 0,
      };
    } catch (e) {
      debugPrint('Error reading backup info: $e');
      return null;
    }
  }
}
