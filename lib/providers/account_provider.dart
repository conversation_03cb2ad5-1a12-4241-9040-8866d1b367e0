import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../database/database_helper.dart';

class AccountProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Account> _accounts = [];
  bool _isLoading = false;

  List<Account> get accounts => _accounts;
  bool get isLoading => _isLoading;

  Future<void> loadAccounts() async {
    _isLoading = true;
    notifyListeners();

    try {
      _accounts = await _databaseHelper.getAccounts();
    } catch (e) {
      debugPrint('Error loading accounts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addAccount(Account account) async {
    try {
      final id = await _databaseHelper.insertAccount(account);
      if (id > 0) {
        await loadAccounts();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error adding account: $e');
      return false;
    }
  }

  Future<bool> updateAccount(Account account) async {
    try {
      final result = await _databaseHelper.updateAccount(account);
      if (result > 0) {
        await loadAccounts();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating account: $e');
      return false;
    }
  }

  Future<bool> deleteAccount(int id) async {
    try {
      final result = await _databaseHelper.deleteAccount(id);
      if (result > 0) {
        _accounts.removeWhere((account) => account.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting account: $e');
      return false;
    }
  }

  Account? getAccountById(int id) {
    try {
      return _accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  Account? get defaultAccount {
    try {
      return _accounts.firstWhere((account) => account.isDefault);
    } catch (e) {
      return _accounts.isNotEmpty ? _accounts.first : null;
    }
  }
}
