import 'package:flutter/foundation.dart';
import '../models/saving_goal.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';

class SavingGoalProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<SavingGoal> _savingGoals = [];
  bool _isLoading = false;

  List<SavingGoal> get savingGoals => _savingGoals;
  bool get isLoading => _isLoading;

  List<SavingGoal> get activeSavingGoals => 
      _savingGoals.where((goal) => !goal.isCompleted).toList();

  List<SavingGoal> get completedSavingGoals => 
      _savingGoals.where((goal) => goal.isCompleted).toList();

  Future<void> loadSavingGoals({bool? isCompleted}) async {
    _isLoading = true;
    notifyListeners();

    try {
      _savingGoals = await _databaseHelper.getSavingGoals(isCompleted: isCompleted);
    } catch (e) {
      debugPrint('Error loading saving goals: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addSavingGoal(SavingGoal goal) async {
    try {
      final id = await _databaseHelper.insertSavingGoal(goal);
      if (id > 0) {
        await loadSavingGoals();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error adding saving goal: $e');
      return false;
    }
  }

  Future<bool> updateSavingGoal(SavingGoal goal) async {
    try {
      final result = await _databaseHelper.updateSavingGoal(goal);
      if (result > 0) {
        await loadSavingGoals();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating saving goal: $e');
      return false;
    }
  }

  Future<bool> deleteSavingGoal(int id) async {
    try {
      final result = await _databaseHelper.deleteSavingGoal(id);
      if (result > 0) {
        _savingGoals.removeWhere((goal) => goal.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting saving goal: $e');
      return false;
    }
  }

  Future<bool> addToSavingGoal(int goalId, double amount, {int? accountId}) async {
    try {
      final goal = _savingGoals.firstWhere((g) => g.id == goalId);

      // Get savings category
      final savingsCategory = await _databaseHelper.getSavingsCategory();
      if (savingsCategory == null) {
        debugPrint('Savings category not found');
        return false;
      }

      // Get default account if not specified
      int selectedAccountId = accountId ?? 1; // Default to first account
      if (accountId == null) {
        final accounts = await _databaseHelper.getAccounts();
        if (accounts.isNotEmpty) {
          selectedAccountId = accounts.first.id!;
        }
      }

      // Create expense transaction to deduct from total balance
      final now = DateTime.now();
      final transaction = Transaction(
        amount: amount,
        type: 'expense',
        categoryId: savingsCategory.id!,
        accountId: selectedAccountId,
        date: now,
        note: 'Tiết kiệm cho mục tiêu: ${goal.name}',
        createdAt: now,
        updatedAt: now,
      );

      // Insert transaction first
      final transactionId = await _databaseHelper.insertTransaction(transaction);
      if (transactionId <= 0) {
        debugPrint('Failed to create savings transaction');
        return false;
      }

      // Update saving goal
      final updatedGoal = goal.copyWith(
        currentAmount: goal.currentAmount + amount,
        isCompleted: (goal.currentAmount + amount) >= goal.targetAmount,
        updatedAt: now,
      );

      final success = await updateSavingGoal(updatedGoal);
      if (!success) {
        // If saving goal update fails, delete the transaction
        await _databaseHelper.deleteTransaction(transactionId);
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error adding to saving goal: $e');
      return false;
    }
  }

  SavingGoal? getSavingGoalById(int id) {
    try {
      return _savingGoals.firstWhere((goal) => goal.id == id);
    } catch (e) {
      return null;
    }
  }

  List<SavingGoal> getUpcomingDeadlines({int days = 30}) {
    final cutoffDate = DateTime.now().add(Duration(days: days));
    
    return _savingGoals.where((goal) {
      return !goal.isCompleted && 
             goal.targetDate.isBefore(cutoffDate) &&
             goal.targetDate.isAfter(DateTime.now());
    }).toList()..sort((a, b) => a.targetDate.compareTo(b.targetDate));
  }

  double getTotalSavingsTarget() {
    return _savingGoals
        .where((goal) => !goal.isCompleted)
        .fold(0.0, (sum, goal) => sum + goal.targetAmount);
  }

  double getTotalCurrentSavings() {
    return _savingGoals
        .where((goal) => !goal.isCompleted)
        .fold(0.0, (sum, goal) => sum + goal.currentAmount);
  }
}
