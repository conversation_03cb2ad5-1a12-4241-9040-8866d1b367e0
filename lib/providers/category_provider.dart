import 'package:flutter/foundation.dart';
import '../models/category.dart' as model_category;
import '../database/database_helper.dart';

class CategoryProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<model_category.Category> _categories = [];
  bool _isLoading = false;

  List<model_category.Category> get categories => _categories;
  bool get isLoading => _isLoading;

  List<model_category.Category> get incomeCategories => 
      _categories.where((model_category.Category category) => category.type == 'income').toList();

  List<model_category.Category> get expenseCategories => 
      _categories.where((model_category.Category category) => category.type == 'expense').toList();

  Future<void> loadCategories({String? type}) async {
    _isLoading = true;
    notifyListeners();

    try {
      _categories = await _databaseHelper.getCategories(type: type);
    } catch (e) {
      debugPrint('Error loading categories: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addCategory(model_category.Category category) async {
    try {
      final id = await _databaseHelper.insertCategory(category);
      if (id > 0) {
        await loadCategories();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error adding category: $e');
      return false;
    }
  }

  Future<bool> updateCategory(model_category.Category category) async {
    try {
      final result = await _databaseHelper.updateCategory(category);
      if (result > 0) {
        await loadCategories();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating category: $e');
      return false;
    }
  }

  Future<bool> deleteCategory(int id) async {
    try {
      final result = await _databaseHelper.deleteCategory(id);
      if (result > 0) {
        _categories.removeWhere((model_category.Category category) => category.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting category: $e');
      return false;
    }
  }

  model_category.Category? getCategoryById(int id) {
    try {
      return _categories.firstWhere((model_category.Category category) => category.id == id);
    } catch (e) {
      return null;
    }
  }
}
