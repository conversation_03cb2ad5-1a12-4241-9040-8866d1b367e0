import 'package:flutter/foundation.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';

class TransactionProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Transaction> _transactions = [];
  bool _isLoading = false;

  List<Transaction> get transactions => _transactions;
  bool get isLoading => _isLoading;

  Future<void> loadTransactions({
    String? type,
    int? categoryId,
    int? accountId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      _transactions = await _databaseHelper.getTransactions(
        type: type,
        categoryId: categoryId,
        accountId: accountId,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );
    } catch (e) {
      debugPrint('Error loading transactions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addTransaction(Transaction transaction) async {
    try {
      final id = await _databaseHelper.insertTransaction(transaction);
      if (id > 0) {
        await loadTransactions(); // Reload to get updated list
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error adding transaction: $e');
      return false;
    }
  }

  Future<bool> updateTransaction(Transaction transaction) async {
    try {
      final result = await _databaseHelper.updateTransaction(transaction);
      if (result > 0) {
        await loadTransactions(); // Reload to get updated list
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating transaction: $e');
      return false;
    }
  }

  Future<bool> deleteTransaction(int id) async {
    try {
      final result = await _databaseHelper.deleteTransaction(id);
      if (result > 0) {
        _transactions.removeWhere((transaction) => transaction.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting transaction: $e');
      return false;
    }
  }

  List<Transaction> getTransactionsByDateRange(DateTime startDate, DateTime endDate) {
    return _transactions.where((transaction) {
      return transaction.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
             transaction.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  double getTotalAmount({String? type, DateTime? startDate, DateTime? endDate}) {
    var filteredTransactions = _transactions;

    if (type != null) {
      filteredTransactions = filteredTransactions.where((t) => t.type == type).toList();
    }

    if (startDate != null && endDate != null) {
      filteredTransactions = filteredTransactions.where((t) {
        return t.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
               t.date.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    return filteredTransactions.fold(0.0, (sum, transaction) => sum + transaction.amount);
  }

  Map<int, double> getCategoryTotals({String? type, DateTime? startDate, DateTime? endDate}) {
    var filteredTransactions = _transactions;

    if (type != null) {
      filteredTransactions = filteredTransactions.where((t) => t.type == type).toList();
    }

    if (startDate != null && endDate != null) {
      filteredTransactions = filteredTransactions.where((t) {
        return t.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
               t.date.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    Map<int, double> categoryTotals = {};
    for (var transaction in filteredTransactions) {
      categoryTotals[transaction.categoryId] = 
          (categoryTotals[transaction.categoryId] ?? 0) + transaction.amount;
    }

    return categoryTotals;
  }
}
