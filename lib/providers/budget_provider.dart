import 'package:flutter/foundation.dart';
import '../models/budget.dart';
import '../database/database_helper.dart';

class BudgetProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Budget> _budgets = [];
  bool _isLoading = false;

  List<Budget> get budgets => _budgets;
  bool get isLoading => _isLoading;

  List<Budget> get activeBudgets => 
      _budgets.where((budget) => budget.isActive).toList();

  Future<void> loadBudgets({bool? isActive}) async {
    _isLoading = true;
    notifyListeners();

    try {
      _budgets = await _databaseHelper.getBudgets(isActive: isActive);
    } catch (e) {
      debugPrint('Error loading budgets: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addBudget(Budget budget) async {
    try {
      final id = await _databaseHelper.insertBudget(budget);
      if (id > 0) {
        await loadBudgets();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error adding budget: $e');
      return false;
    }
  }

  Future<bool> updateBudget(Budget budget) async {
    try {
      final result = await _databaseHelper.updateBudget(budget);
      if (result > 0) {
        await loadBudgets();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating budget: $e');
      return false;
    }
  }

  Future<bool> deleteBudget(int id) async {
    try {
      final result = await _databaseHelper.deleteBudget(id);
      if (result > 0) {
        _budgets.removeWhere((budget) => budget.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting budget: $e');
      return false;
    }
  }

  Budget? getBudgetById(int id) {
    try {
      return _budgets.firstWhere((budget) => budget.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Budget> getCurrentMonthBudgets() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    return _budgets.where((budget) {
      return budget.isActive &&
             budget.startDate.isBefore(endOfMonth) &&
             budget.endDate.isAfter(startOfMonth);
    }).toList();
  }

  double getBudgetProgress(Budget budget, double spentAmount) {
    if (budget.amount == 0) return 0;
    return (spentAmount / budget.amount * 100).clamp(0, 100);
  }

  bool isBudgetExceeded(Budget budget, double spentAmount) {
    return spentAmount > budget.amount;
  }
}
