import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/transaction_provider.dart';
import '../providers/category_provider.dart';
import '../providers/account_provider.dart';
import '../providers/budget_provider.dart';
import '../providers/budget_provider.dart';
import '../providers/saving_goal_provider.dart';
import 'add_transaction_screen.dart';
import 'transactions_screen.dart';
import 'reports_screen.dart';
import 'budgets_screen.dart';
import 'savings_screen.dart';
import 'backup_screen.dart';
import '../database/database_helper.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  double _totalBalance = 0.0;
  Map<String, double> _monthlyTotals = {'income': 0.0, 'expense': 0.0};

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    _loadProviderData();
    await _loadDashboardData();
  }

  Future<void> _loadProviderData() async {
    // Use WidgetsBinding.instance.addPostFrameCallback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);
      final budgetProvider = Provider.of<BudgetProvider>(context, listen: false);
      final savingGoalProvider = Provider.of<SavingGoalProvider>(context, listen: false);

      await Future.wait([
        transactionProvider.loadTransactions(limit: 10),
        categoryProvider.loadCategories(),
        accountProvider.loadAccounts(),
        budgetProvider.loadBudgets(),
        savingGoalProvider.loadSavingGoals(),
      ]);
    });
  }

  Future<void> _loadDashboardData() async {
    final balance = await _databaseHelper.getTotalBalance();
    final monthlyTotals = await _databaseHelper.getMonthlyTotals(DateTime.now());
    
    setState(() {
      _totalBalance = balance;
      _monthlyTotals = monthlyTotals;
    });
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> screens = [
      _buildDashboardContent(),
      const TransactionsScreen(),
      ReportsScreen(key: ValueKey(_selectedIndex == 2 ? DateTime.now().millisecondsSinceEpoch : 0)),
      const BudgetsScreen(),
      const SavingsScreen(),
    ];

    return Scaffold(
      appBar: _selectedIndex == 0 ? AppBar(
        title: const Text('Vitien - Quản lý tài chính'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ) : null,
      drawer: _selectedIndex == 0 ? _buildDrawer() : null,
      body: IndexedStack(
        index: _selectedIndex,
        children: screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
          // Refresh reports screen when tapping on reports tab
          if (index == 2) {
            // Force rebuild of ReportsScreen by updating the key
            setState(() {});
          }
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Tổng quan',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.list),
            label: 'Giao dịch',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart),
            label: 'Báo cáo',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'Ngân sách',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.savings),
            label: 'Tiết kiệm',
          ),
        ],
      ),
      floatingActionButton: _selectedIndex == 0 || _selectedIndex == 1
          ? FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddTransactionScreen(),
                  ),
                ).then((_) {
                  _loadDashboardData();
                  // Refresh providers to update all screens
                  _loadProviderData();
                });
              },
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildDashboardContent() {
    return RefreshIndicator(
      onRefresh: _loadInitialData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBalanceCard(),
            const SizedBox(height: 16),
            _buildMonthlyOverviewCard(),
            const SizedBox(height: 16),
            _buildQuickActionsCard(),
            const SizedBox(height: 16),
            _buildRecentTransactionsCard(),
            const SizedBox(height: 16),
            _buildBudgetAlertsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withOpacity(0.8),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tổng số dư',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              NumberFormat.currency(locale: 'vi_VN', symbol: '₫').format(_totalBalance),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyOverviewCard() {
    final income = _monthlyTotals['income'] ?? 0.0;
    final expense = _monthlyTotals['expense'] ?? 0.0;
    final net = income - expense;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tháng ${DateFormat('MM/yyyy').format(DateTime.now())}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildOverviewItem(
                    'Thu nhập',
                    income,
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildOverviewItem(
                    'Chi tiêu',
                    expense,
                    Icons.trending_down,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildOverviewItem(
                    'Còn lại',
                    net,
                    net >= 0 ? Icons.add : Icons.remove,
                    net >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewItem(String title, double amount, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          NumberFormat.compact(locale: 'vi_VN').format(amount),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thao tác nhanh',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickActionButton(
                  'Thu nhập',
                  Icons.add_circle,
                  Colors.green,
                  () => _navigateToAddTransaction('income'),
                ),
                _buildQuickActionButton(
                  'Chi tiêu',
                  Icons.remove_circle,
                  Colors.red,
                  () => _navigateToAddTransaction('expense'),
                ),
                _buildQuickActionButton(
                  'Chuyển khoản',
                  Icons.swap_horiz,
                  Colors.blue,
                  () {
                    // TODO: Implement transfer functionality
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(String title, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Giao dịch gần đây',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedIndex = 1;
                    });
                  },
                  child: const Text('Xem tất cả'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Consumer<TransactionProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (provider.transactions.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: Text('Chưa có giao dịch nào'),
                    ),
                  );
                }

                return ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: provider.transactions.take(5).length,
                  separatorBuilder: (context, index) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final transaction = provider.transactions[index];
                    return _buildTransactionItem(transaction);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(transaction) {
    return Consumer2<CategoryProvider, AccountProvider>(
      builder: (context, categoryProvider, accountProvider, child) {
        final category = categoryProvider.getCategoryById(transaction.categoryId);
        final account = accountProvider.getAccountById(transaction.accountId);
        
        return ListTile(
          contentPadding: EdgeInsets.zero,
          leading: CircleAvatar(
            backgroundColor: category != null 
                ? Color(int.parse(category.color.replaceFirst('#', '0xff')))
                : Colors.grey,
            child: Icon(
              _getIconData(category?.icon ?? 'help'),
              color: Colors.white,
              size: 20,
            ),
          ),
          title: Text(
            category?.name ?? 'Không xác định',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: Text(
            '${account?.name ?? 'Không xác định'} • ${DateFormat('dd/MM/yyyy').format(transaction.date)}',
          ),
          trailing: Text(
            '${transaction.type == 'income' ? '+' : '-'}${NumberFormat.compact(locale: 'vi_VN').format(transaction.amount)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: transaction.type == 'income' ? Colors.green : Colors.red,
            ),
          ),
        );
      },
    );
  }

  Widget _buildBudgetAlertsCard() {
    return Consumer<BudgetProvider>(
      builder: (context, provider, child) {
        final currentBudgets = provider.getCurrentMonthBudgets();
        
        if (currentBudgets.isEmpty) {
          return const SizedBox.shrink();
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Cảnh báo ngân sách',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                // TODO: Implement budget alerts based on spending
                const Text('Tính năng cảnh báo ngân sách sẽ được cập nhật sau.'),
              ],
            ),
          ),
        );
      },
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant': return Icons.restaurant;
      case 'directions_car': return Icons.directions_car;
      case 'home': return Icons.home;
      case 'receipt': return Icons.receipt;
      case 'movie': return Icons.movie;
      case 'school': return Icons.school;
      case 'local_hospital': return Icons.local_hospital;
      case 'shopping_cart': return Icons.shopping_cart;
      case 'work': return Icons.work;
      case 'card_giftcard': return Icons.card_giftcard;
      case 'business': return Icons.business;
      case 'trending_up': return Icons.trending_up;
      case 'wallet': return Icons.account_balance_wallet;
      case 'account_balance': return Icons.account_balance;
      case 'phone_android': return Icons.phone_android;
      default: return Icons.help;
    }
  }

  void _navigateToAddTransaction(String type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddTransactionScreen(initialType: type),
      ),
    ).then((_) {
      _loadDashboardData();
      // Refresh providers to update all screens
      _loadProviderData();
    });
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const DrawerHeader(
            decoration: BoxDecoration(
              color: Colors.blue,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  size: 48,
                  color: Colors.white,
                ),
                SizedBox(height: 8),
                Text(
                  'Vitien',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Quản lý tài chính cá nhân',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.backup),
            title: const Text('Sao lưu & Khôi phục'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BackupScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.security),
            title: const Text('Bảo mật'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to security settings
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Cài đặt'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to settings
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('Về ứng dụng'),
            onTap: () {
              Navigator.pop(context);
              showAboutDialog(
                context: context,
                applicationName: 'Vitien',
                applicationVersion: '1.0.0',
                applicationIcon: const Icon(Icons.account_balance_wallet),
                children: [
                  const Text('Ứng dụng quản lý tài chính cá nhân offline'),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
