import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../models/transaction.dart';
import '../models/category.dart';
import '../models/account.dart';
import '../providers/transaction_provider.dart';
import '../providers/category_provider.dart';
import '../providers/account_provider.dart';

class AddTransactionScreen extends StatefulWidget {
  final String? initialType;
  final Transaction? transaction;

  const AddTransactionScreen({
    super.key,
    this.initialType,
    this.transaction,
  });

  @override
  State<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends State<AddTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _noteController = TextEditingController();
  
  String _selectedType = 'expense';
  Category? _selectedCategory;
  Account? _selectedAccount;
  DateTime _selectedDate = DateTime.now();
  File? _selectedImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.initialType ?? 'expense';
    
    if (widget.transaction != null) {
      _initializeWithTransaction();
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _initializeWithTransaction() {
    final transaction = widget.transaction!;
    _amountController.text = transaction.amount.toString();
    _noteController.text = transaction.note ?? '';
    _selectedType = transaction.type;
    _selectedDate = transaction.date;
  }

  Future<void> _loadData() async {
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
    final accountProvider = Provider.of<AccountProvider>(context, listen: false);
    
    await Future.wait([
      categoryProvider.loadCategories(),
      accountProvider.loadAccounts(),
    ]);

    if (widget.transaction != null) {
      setState(() {
        _selectedCategory = categoryProvider.getCategoryById(widget.transaction!.categoryId);
        _selectedAccount = accountProvider.getAccountById(widget.transaction!.accountId);
      });
    } else {
      // Set default account
      final accounts = accountProvider.accounts;
      if (accounts.isNotEmpty) {
        setState(() {
          _selectedAccount = accountProvider.defaultAccount ?? accounts.first;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.transaction != null ? 'Sửa giao dịch' : 'Thêm giao dịch'),
        actions: [
          if (widget.transaction != null)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteTransaction,
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTypeSelector(),
              const SizedBox(height: 24),
              _buildAmountField(),
              const SizedBox(height: 24),
              _buildCategorySelector(),
              const SizedBox(height: 24),
              _buildAccountSelector(),
              const SizedBox(height: 24),
              _buildDateSelector(),
              const SizedBox(height: 24),
              _buildNoteField(),
              const SizedBox(height: 24),
              _buildImageSection(),
              const SizedBox(height: 32),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loại giao dịch',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildTypeOption('income', 'Thu nhập', Icons.add_circle, Colors.green),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTypeOption('expense', 'Chi tiêu', Icons.remove_circle, Colors.red),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTypeOption(String type, String label, IconData icon, Color color) {
    final isSelected = _selectedType == type;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedType = type;
          _selectedCategory = null; // Reset category when type changes
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected ? color.withOpacity(0.1) : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Số tiền',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _amountController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            hintText: '0',
            suffixText: '₫',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập số tiền';
            }
            if (double.tryParse(value) == null || double.parse(value) <= 0) {
              return 'Số tiền phải lớn hơn 0';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCategorySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Danh mục',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Consumer<CategoryProvider>(
          builder: (context, provider, child) {
            final categories = _selectedType == 'income' 
                ? provider.incomeCategories 
                : provider.expenseCategories;

            if (categories.isEmpty) {
              return const Text('Không có danh mục nào');
            }

            return InkWell(
              onTap: () => _showCategoryPicker(categories),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    if (_selectedCategory != null) ...[
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: Color(int.parse(_selectedCategory!.color.replaceFirst('#', '0xff'))),
                        child: Icon(
                          _getIconData(_selectedCategory!.icon),
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(_selectedCategory!.name),
                    ] else
                      const Text('Chọn danh mục'),
                    const Spacer(),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAccountSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tài khoản',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Consumer<AccountProvider>(
          builder: (context, provider, child) {
            if (provider.accounts.isEmpty) {
              return const Text('Không có tài khoản nào');
            }

            return InkWell(
              onTap: () => _showAccountPicker(provider.accounts),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    if (_selectedAccount != null) ...[
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: Color(int.parse(_selectedAccount!.color.replaceFirst('#', '0xff'))),
                        child: Icon(
                          _getIconData(_selectedAccount!.icon),
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(_selectedAccount!.name),
                    ] else
                      const Text('Chọn tài khoản'),
                    const Spacer(),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ngày',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        InkWell(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today),
                const SizedBox(width: 12),
                Text(DateFormat('dd/MM/yyyy').format(_selectedDate)),
                const Spacer(),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNoteField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ghi chú',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _noteController,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'Nhập ghi chú (tùy chọn)',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Hình ảnh hóa đơn',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (_selectedImage != null) ...[
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: FileImage(_selectedImage!),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              TextButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.edit),
                label: const Text('Thay đổi'),
              ),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedImage = null;
                  });
                },
                icon: const Icon(Icons.delete),
                label: const Text('Xóa'),
              ),
            ],
          ),
        ] else
          OutlinedButton.icon(
            onPressed: _pickImage,
            icon: const Icon(Icons.camera_alt),
            label: const Text('Thêm hình ảnh'),
          ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveTransaction,
        child: _isLoading
            ? const CircularProgressIndicator()
            : Text(widget.transaction != null ? 'Cập nhật' : 'Lưu'),
      ),
    );
  }

  Future<void> _showCategoryPicker(List<Category> categories) async {
    final selected = await showModalBottomSheet<Category>(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Chọn danh mục',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Color(int.parse(category.color.replaceFirst('#', '0xff'))),
                      child: Icon(
                        _getIconData(category.icon),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(category.name),
                    onTap: () => Navigator.pop(context, category),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );

    if (selected != null) {
      setState(() {
        _selectedCategory = selected;
      });
    }
  }

  Future<void> _showAccountPicker(List<Account> accounts) async {
    final selected = await showModalBottomSheet<Account>(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Chọn tài khoản',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: accounts.length,
                itemBuilder: (context, index) {
                  final account = accounts[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Color(int.parse(account.color.replaceFirst('#', '0xff'))),
                      child: Icon(
                        _getIconData(account.icon),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(account.name),
                    subtitle: Text(account.type),
                    onTap: () => Navigator.pop(context, account),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );

    if (selected != null) {
      setState(() {
        _selectedAccount = selected;
      });
    }
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.camera);

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vui lòng chọn danh mục')),
      );
      return;
    }
    if (_selectedAccount == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vui lòng chọn tài khoản')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      final now = DateTime.now();
      
      final transaction = Transaction(
        id: widget.transaction?.id,
        amount: amount,
        type: _selectedType,
        categoryId: _selectedCategory!.id!,
        accountId: _selectedAccount!.id!,
        date: _selectedDate,
        note: _noteController.text.isEmpty ? null : _noteController.text,
        imagePath: _selectedImage?.path,
        createdAt: widget.transaction?.createdAt ?? now,
        updatedAt: now,
      );

      final provider = Provider.of<TransactionProvider>(context, listen: false);
      bool success;
      
      if (widget.transaction != null) {
        success = await provider.updateTransaction(transaction);
      } else {
        success = await provider.addTransaction(transaction);
      }

      if (success) {
        if (mounted) {
          Navigator.pop(context, true); // Return true to indicate data was saved
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.transaction != null 
                ? 'Cập nhật giao dịch thành công' 
                : 'Thêm giao dịch thành công'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Có lỗi xảy ra, vui lòng thử lại')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Có lỗi xảy ra, vui lòng thử lại')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteTransaction() async {
    if (widget.transaction == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: const Text('Bạn có chắc chắn muốn xóa giao dịch này?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      final success = await provider.deleteTransaction(widget.transaction!.id!);
      
      if (mounted) {
        if (success) {
          Navigator.pop(context, true); // Return true to indicate data was changed
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Xóa giao dịch thành công')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Có lỗi xảy ra, vui lòng thử lại')),
          );
        }
      }
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant': return Icons.restaurant;
      case 'directions_car': return Icons.directions_car;
      case 'home': return Icons.home;
      case 'receipt': return Icons.receipt;
      case 'movie': return Icons.movie;
      case 'school': return Icons.school;
      case 'local_hospital': return Icons.local_hospital;
      case 'shopping_cart': return Icons.shopping_cart;
      case 'work': return Icons.work;
      case 'card_giftcard': return Icons.card_giftcard;
      case 'business': return Icons.business;
      case 'trending_up': return Icons.trending_up;
      case 'wallet': return Icons.account_balance_wallet;
      case 'account_balance': return Icons.account_balance;
      case 'phone_android': return Icons.phone_android;
      default: return Icons.help;
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _noteController.dispose();
    super.dispose();
  }
}
