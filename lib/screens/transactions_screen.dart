import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/transaction_provider.dart';
import '../providers/category_provider.dart';
import '../providers/account_provider.dart';
import 'add_transaction_screen.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  String? _selectedType;
  int? _selectedCategoryId;
  int? _selectedAccountId;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTransactions();
    });
  }

  Future<void> _loadTransactions() async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.loadTransactions(
      type: _selectedType,
      categoryId: _selectedCategoryId,
      accountId: _selectedAccountId,
      startDate: _startDate,
      endDate: _endDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildFilterSection(),
          Expanded(
            child: _buildTransactionsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildFilterChip(
                  'Tất cả',
                  _selectedType == null,
                  () => _updateFilter(type: null),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFilterChip(
                  'Thu nhập',
                  _selectedType == 'income',
                  () => _updateFilter(type: 'income'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFilterChip(
                  'Chi tiêu',
                  _selectedType == 'expense',
                  () => _updateFilter(type: 'expense'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showDateRangePicker,
                  icon: const Icon(Icons.date_range, size: 18),
                  label: Text(
                    _startDate != null && _endDate != null
                        ? '${DateFormat('dd/MM').format(_startDate!)} - ${DateFormat('dd/MM').format(_endDate!)}'
                        : 'Chọn khoảng thời gian',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: _showMoreFilters,
                icon: const Icon(Icons.filter_list, size: 18),
                label: const Text('Lọc', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary 
                : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionsList() {
    return Consumer3<TransactionProvider, CategoryProvider, AccountProvider>(
      builder: (context, transactionProvider, categoryProvider, accountProvider, child) {
        if (transactionProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (transactionProvider.transactions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'Chưa có giao dịch nào',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Nhấn nút + để thêm giao dịch đầu tiên',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          );
        }

        // Group transactions by date
        final groupedTransactions = <String, List<dynamic>>{};
        for (final transaction in transactionProvider.transactions) {
          final dateKey = DateFormat('yyyy-MM-dd').format(transaction.date);
          groupedTransactions[dateKey] ??= [];
          groupedTransactions[dateKey]!.add(transaction);
        }

        final sortedDates = groupedTransactions.keys.toList()
          ..sort((a, b) => b.compareTo(a));

        return RefreshIndicator(
          onRefresh: _loadTransactions,
          child: ListView.builder(
            itemCount: sortedDates.length,
            itemBuilder: (context, index) {
              final dateKey = sortedDates[index];
              final transactions = groupedTransactions[dateKey]!;
              final date = DateTime.parse(dateKey);
              
              return _buildDateGroup(date, transactions, categoryProvider, accountProvider);
            },
          ),
        );
      },
    );
  }

  Widget _buildDateGroup(DateTime date, List<dynamic> transactions, 
      CategoryProvider categoryProvider, AccountProvider accountProvider) {
    final dayTotal = transactions.fold<double>(0, (sum, transaction) {
      return sum + (transaction.type == 'income' ? transaction.amount : -transaction.amount);
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          color: Colors.grey.shade50,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDateHeader(date),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Text(
                '${dayTotal >= 0 ? '+' : ''}${NumberFormat.compact(locale: 'vi_VN').format(dayTotal)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: dayTotal >= 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: transactions.length,
          separatorBuilder: (context, index) => const Divider(height: 1, indent: 72),
          itemBuilder: (context, index) {
            final transaction = transactions[index];
            return _buildTransactionItem(transaction, categoryProvider, accountProvider);
          },
        ),
      ],
    );
  }

  Widget _buildTransactionItem(dynamic transaction, 
      CategoryProvider categoryProvider, AccountProvider accountProvider) {
    final category = categoryProvider.getCategoryById(transaction.categoryId);
    final account = accountProvider.getAccountById(transaction.accountId);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: category != null 
            ? Color(int.parse(category.color.replaceFirst('#', '0xff')))
            : Colors.grey,
        child: Icon(
          _getIconData(category?.icon ?? 'help'),
          color: Colors.white,
          size: 20,
        ),
      ),
      title: Text(
        category?.name ?? 'Không xác định',
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(account?.name ?? 'Không xác định'),
          if (transaction.note != null && transaction.note!.isNotEmpty)
            Text(
              transaction.note!,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${transaction.type == 'income' ? '+' : '-'}${NumberFormat.compact(locale: 'vi_VN').format(transaction.amount)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: transaction.type == 'income' ? Colors.green : Colors.red,
            ),
          ),
          Text(
            DateFormat('HH:mm').format(transaction.date),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
      onTap: () => _editTransaction(transaction),
    );
  }

  String _formatDateHeader(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final transactionDate = DateTime(date.year, date.month, date.day);

    if (transactionDate == today) {
      return 'Hôm nay';
    } else if (transactionDate == yesterday) {
      return 'Hôm qua';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  void _updateFilter({String? type}) {
    setState(() {
      _selectedType = type;
    });
    _loadTransactions();
  }

  Future<void> _showDateRangePicker() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadTransactions();
    }
  }

  Future<void> _showMoreFilters() async {
    await showModalBottomSheet(
      context: context,
      builder: (context) => _buildFilterBottomSheet(),
    );
  }

  Widget _buildFilterBottomSheet() {
    return Consumer2<CategoryProvider, AccountProvider>(
      builder: (context, categoryProvider, accountProvider, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bộ lọc nâng cao',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Text(
                'Danh mục',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: categoryProvider.categories.map<Widget>((category) {
                  final isSelected = _selectedCategoryId == category.id;
                  return FilterChip(
                    label: Text(category.name),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategoryId = selected ? category.id : null;
                      });
                      Navigator.pop(context);
                      _loadTransactions();
                    },
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
              Text(
                'Tài khoản',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: accountProvider.accounts.map<Widget>((account) {
                  final isSelected = _selectedAccountId == account.id;
                  return FilterChip(
                    label: Text(account.name),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedAccountId = selected ? account.id : null;
                      });
                      Navigator.pop(context);
                      _loadTransactions();
                    },
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _selectedType = null;
                          _selectedCategoryId = null;
                          _selectedAccountId = null;
                          _startDate = null;
                          _endDate = null;
                        });
                        Navigator.pop(context);
                        _loadTransactions();
                      },
                      child: const Text('Xóa bộ lọc'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Đóng'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _editTransaction(dynamic transaction) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddTransactionScreen(
          transaction: transaction,
        ),
      ),
    ).then((_) {
      _loadTransactions();
      Provider.of<TransactionProvider>(context, listen: false).loadTransactions();
    });
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant': return Icons.restaurant;
      case 'directions_car': return Icons.directions_car;
      case 'home': return Icons.home;
      case 'receipt': return Icons.receipt;
      case 'movie': return Icons.movie;
      case 'school': return Icons.school;
      case 'local_hospital': return Icons.local_hospital;
      case 'shopping_cart': return Icons.shopping_cart;
      case 'work': return Icons.work;
      case 'card_giftcard': return Icons.card_giftcard;
      case 'business': return Icons.business;
      case 'trending_up': return Icons.trending_up;
      case 'wallet': return Icons.account_balance_wallet;
      case 'account_balance': return Icons.account_balance;
      case 'phone_android': return Icons.phone_android;
      default: return Icons.help;
    }
  }
}
