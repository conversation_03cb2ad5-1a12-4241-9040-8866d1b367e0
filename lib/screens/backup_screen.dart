import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import '../services/backup_service.dart';

class BackupScreen extends StatefulWidget {
  const BackupScreen({super.key});

  @override
  State<BackupScreen> createState() => _BackupScreenState();
}

class _BackupScreenState extends State<BackupScreen> {
  final BackupService _backupService = BackupService();
  List<String> _availableBackups = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAvailableBackups();
  }

  Future<void> _loadAvailableBackups() async {
    setState(() => _isLoading = true);
    try {
      final backups = await _backupService.getAvailableBackups();
      setState(() => _availableBackups = backups);
    } catch (e) {
      _showErrorSnackBar('Error loading backups: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportToJson() async {
    setState(() => _isLoading = true);
    try {
      final filePath = await _backupService.exportToJson();
      if (filePath != null) {
        _showSuccessSnackBar('Backup exported to: ${_backupService.getBackupFileName(filePath)}');
        await _loadAvailableBackups();
      } else {
        _showErrorSnackBar('Failed to export backup');
      }
    } catch (e) {
      _showErrorSnackBar('Export error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportToCsv() async {
    setState(() => _isLoading = true);
    try {
      final filePath = await _backupService.exportToCsv();
      if (filePath != null) {
        _showSuccessSnackBar('Transactions exported to CSV: ${_backupService.getBackupFileName(filePath)}');
      } else {
        _showErrorSnackBar('Failed to export CSV');
      }
    } catch (e) {
      _showErrorSnackBar('CSV export error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _importFromFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        
        // Show confirmation dialog
        final confirm = await _showImportConfirmationDialog();
        if (!confirm) return;

        setState(() => _isLoading = true);
        
        final success = await _backupService.importFromJson(filePath);
        if (success) {
          _showSuccessSnackBar('Data imported successfully');
        } else {
          _showErrorSnackBar('Failed to import data');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Import error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _importBackup(String filePath) async {
    final confirm = await _showImportConfirmationDialog();
    if (!confirm) return;

    setState(() => _isLoading = true);
    try {
      final success = await _backupService.importFromJson(filePath);
      if (success) {
        _showSuccessSnackBar('Backup restored successfully');
      } else {
        _showErrorSnackBar('Failed to restore backup');
      }
    } catch (e) {
      _showErrorSnackBar('Restore error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<bool> _showImportConfirmationDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Confirmation'),
        content: const Text(
          'Importing will add data to your existing records. This action cannot be undone. Continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Import'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _showBackupInfo(String filePath) async {
    final info = await _backupService.getBackupInfo(filePath);
    if (info == null) {
      _showErrorSnackBar('Could not read backup file');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_backupService.getBackupFileName(filePath)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Version: ${info['version']}'),
            Text('Export Date: ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.parse(info['exportDate']))}'),
            const SizedBox(height: 16),
            Text('Transactions: ${info['transactionCount']}'),
            Text('Categories: ${info['categoryCount']}'),
            Text('Accounts: ${info['accountCount']}'),
            Text('Budgets: ${info['budgetCount']}'),
            Text('Saving Goals: ${info['savingGoalCount']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _importBackup(filePath);
            },
            child: const Text('Restore'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backup & Restore'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildExportSection(),
                  const SizedBox(height: 24),
                  _buildImportSection(),
                  const SizedBox(height: 24),
                  _buildBackupListSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildExportSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Export Data',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Create a backup of all your financial data',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _exportToJson,
                    icon: const Icon(Icons.backup),
                    label: const Text('Export JSON'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _exportToCsv,
                    icon: const Icon(Icons.table_chart),
                    label: const Text('Export CSV'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Import Data',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Restore data from a backup file',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _importFromFile,
                icon: const Icon(Icons.file_upload),
                label: const Text('Import from File'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupListSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Available Backups',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: _loadAvailableBackups,
                  icon: const Icon(Icons.refresh),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Tap on a backup to view details and restore',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            if (_availableBackups.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    'No backups found',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _availableBackups.length,
                itemBuilder: (context, index) {
                  final backup = _availableBackups[index];
                  final fileName = _backupService.getBackupFileName(backup);
                  
                  return ListTile(
                    leading: const Icon(Icons.backup),
                    title: Text(fileName),
                    subtitle: Text('Tap to view details'),
                    trailing: const Icon(Icons.info_outline),
                    onTap: () => _showBackupInfo(backup),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
